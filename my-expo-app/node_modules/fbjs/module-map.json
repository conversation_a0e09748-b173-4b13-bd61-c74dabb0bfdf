{"BrowserSupportCore": "fbjs/lib/BrowserSupportCore", "CSSCore": "fbjs/lib/CSSCore", "CircularBuffer": "fbjs/lib/CircularBuffer", "DOMMouseMoveTracker": "fbjs/lib/DOMMouseMoveTracker", "DataTransfer": "fbjs/lib/DataTransfer", "Deferred": "fbjs/lib/Deferred", "ErrorUtils": "fbjs/lib/Error<PERSON><PERSON>s", "EventListener": "fbjs/lib/EventListener", "ExecutionEnvironment": "fbjs/lib/ExecutionEnvironment", "Heap": "fbjs/lib/Heap", "IntegerBufferSet": "fbjs/lib/IntegerBufferSet", "Keys": "fbjs/lib/Keys", "Locale": "fbjs/lib/Locale", "PhotosMimeType": "fbjs/lib/PhotosMimeType", "PrefixIntervalTree": "fbjs/lib/PrefixIntervalTree", "Promise": "fbjs/lib/Promise", "PromiseMap": "fbjs/lib/PromiseMap", "ReactWheelHandler": "fbjs/lib/ReactWheelHandler", "Scroll": "fbjs/lib/Scroll", "SiteData": "fbjs/lib/SiteData", "Style": "fbjs/lib/Style", "TokenizeUtil": "fbjs/lib/TokenizeUtil", "TouchEventUtils": "fbjs/lib/TouchEventUtils", "URI": "fbjs/lib/URI", "UnicodeBidi": "fbjs/lib/UnicodeBidi", "UnicodeBidiDirection": "fbjs/lib/UnicodeBidiDirection", "UnicodeBidiService": "fbjs/lib/UnicodeBidiService", "UnicodeCJK": "fbjs/lib/UnicodeCJK", "UnicodeHangulKorean": "fbjs/lib/UnicodeHangulKorean", "UnicodeUtils": "fbjs/lib/UnicodeUtils", "UnicodeUtilsExtra": "fbjs/lib/UnicodeUtilsExtra", "UserAgent": "fbjs/lib/UserAgent", "UserAgentData": "fbjs/lib/UserAgentData", "VersionRange": "fbjs/lib/VersionRange", "_shouldPolyfillES6Collection": "fbjs/lib/_shouldPolyfillES6Collection", "areEqual": "fbjs/lib/areEqual", "base62": "fbjs/lib/base62", "camelize": "fbjs/lib/camelize", "camelizeStyleName": "fbjs/lib/camelizeStyleName", "cancelAnimationFramePolyfill": "fbjs/lib/cancelAnimationFramePolyfill", "clamp": "fbjs/lib/clamp", "compactArray": "fbjs/lib/compactArray", "concatAllArray": "fbjs/lib/concatAllArray", "containsNode": "fbjs/lib/containsNode", "countDistinct": "fbjs/lib/countDistinct", "crc32": "fbjs/lib/crc32", "createArrayFromMixed": "fbjs/lib/createArrayFromMixed", "createNodesFromMarkup": "fbjs/lib/createNodesFromMarkup", "cssVar": "fbjs/lib/cssVar", "cx": "fbjs/lib/cx", "debounceCore": "fbjs/lib/debounce<PERSON>ore", "distinctArray": "fbjs/lib/<PERSON><PERSON><PERSON>y", "emptyFunction": "fbjs/lib/emptyFunction", "emptyObject": "fbjs/lib/emptyObject", "enumerate": "fbjs/lib/enumerate", "equalsIterable": "fbjs/lib/equalsIterable", "equalsSet": "fbjs/lib/equalsSet", "everyObject": "fbjs/lib/everyObject", "everySet": "fbjs/lib/everySet", "fetch": "fbjs/lib/fetch", "fetchWithRetries": "fbjs/lib/fetchWithRetries", "filterObject": "fbjs/lib/filterObject", "flatMapArray": "fbjs/lib/flatMapArray", "flattenArray": "fbjs/lib/flatten<PERSON>rray", "focusNode": "fbjs/lib/focusNode", "forEachObject": "fbjs/lib/forEachObject", "getActiveElement": "fbjs/lib/getActiveElement", "getByPath": "fbjs/lib/getByPath", "getDocumentScrollElement": "fbjs/lib/getDocumentScrollElement", "getElementPosition": "fbjs/lib/getElementPosition", "getElementRect": "fbjs/lib/getElementRect", "getMarkupWrap": "fbjs/lib/getMarkupWrap", "getScrollPosition": "fbjs/lib/getScrollPosition", "getStyleProperty": "fbjs/lib/getStyleProperty", "getUnboundedScrollPosition": "fbjs/lib/getUnboundedScrollPosition", "getVendorPrefixedName": "fbjs/lib/getVendorPrefixedName", "getViewportDimensions": "fbjs/lib/getViewportDimensions", "groupArray": "fbjs/lib/groupArray", "hyphenate": "fbjs/lib/hyphenate", "hyphenateStyleName": "fbjs/lib/hyphenateStyleName", "invariant": "fbjs/lib/invariant", "isEmail": "fbjs/lib/isEmail", "isEmpty": "fbjs/lib/isEmpty", "isEventSupported": "fbjs/lib/isEventSupported", "isInternationalPhoneNumber": "fbjs/lib/isInternationalPhoneNumber", "isNode": "fbjs/lib/isNode", "isTextNode": "fbjs/lib/isTextNode", "joinClasses": "fbjs/lib/joinClasses", "keyMirror": "fbjs/lib/keyMirror", "keyMirrorRecursive": "fbjs/lib/keyMirrorRecursive", "keyOf": "fbjs/lib/keyOf", "mapObject": "fbjs/lib/mapObject", "maxBy": "fbjs/lib/maxBy", "memoizeStringOnly": "fbjs/lib/memoizeStringOnly", "minBy": "fbjs/lib/minBy", "monitorCodeUse": "fbjs/lib/monitorCodeUse", "nativeRequestAnimationFrame": "fbjs/lib/nativeRequestAnimationFrame", "normalizeWheel": "fbjs/lib/normalizeWheel", "nullthrows": "fbjs/lib/nullthrows", "partitionArray": "fbjs/lib/partition<PERSON><PERSON>y", "partitionObject": "fbjs/lib/partitionObject", "partitionObjectByKey": "fbjs/lib/partitionObjectByKey", "performance": "fbjs/lib/performance", "performanceNow": "fbjs/lib/performanceNow", "removeFromArray": "fbjs/lib/removeFromArray", "requestAnimationFrame": "fbjs/lib/requestAnimationFrame", "requestAnimationFramePolyfill": "fbjs/lib/requestAnimationFramePolyfill", "resolveImmediate": "fbjs/lib/resolveImmediate", "setImmediate": "fbjs/lib/setImmediate", "shallowEqual": "fbjs/lib/shallowEqual", "someObject": "fbjs/lib/someObject", "someSet": "fbjs/lib/someSet", "sprintf": "fbjs/lib/sprintf", "translateDOMPositionXY": "fbjs/lib/translateDOMPositionXY", "warning": "fbjs/lib/warning", "xhrSimpleDataSerializer": "fbjs/lib/xhrSimpleDataSerializer"}