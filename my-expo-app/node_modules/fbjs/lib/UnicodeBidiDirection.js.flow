/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @providesModule UnicodeBidiDirection
 * @typechecks
 * @flow
 */

/**
 * Constants to represent text directionality
 *
 * Also defines a *global* direciton, to be used in bidi algorithms as a
 * default fallback direciton, when no better direction is found or provided.
 *
 * NOTE: Use `setGlobalDir()`, or update `initGlobalDir()`, to set the initial
 *       global direction value based on the application.
 *
 * Part of the implementation of Unicode Bidirectional Algorithm (UBA)
 * Unicode Standard Annex #9 (UAX9)
 * http://www.unicode.org/reports/tr9/
 */
'use strict';

const invariant = require("./invariant");

export type BidiDirection = 'LTR' | 'RTL' | 'NEUTRAL';
export type HTMLDir = 'ltr' | 'rtl';
const NEUTRAL = 'NEUTRAL'; // No strong direction

const LTR = 'LTR'; // Left-to-Right direction

const RTL = 'RTL'; // Right-to-Left direction

let globalDir: ?BidiDirection = null; // == Helpers ==

/**
 * Check if a directionality value is a Strong one
 */

function isStrong(dir: BidiDirection): boolean {
  return dir === LTR || dir === RTL;
}
/**
 * Get string value to be used for `dir` HTML attribute or `direction` CSS
 * property.
 */


function getHTMLDir(dir: BidiDirection): HTMLDir {
  invariant(isStrong(dir), '`dir` must be a strong direction to be converted to HTML Direction');
  return dir === LTR ? 'ltr' : 'rtl';
}
/**
 * Get string value to be used for `dir` HTML attribute or `direction` CSS
 * property, but returns null if `dir` has same value as `otherDir`.
 * `null`.
 */


function getHTMLDirIfDifferent(dir: BidiDirection, otherDir: BidiDirection): ?HTMLDir {
  invariant(isStrong(dir), '`dir` must be a strong direction to be converted to HTML Direction');
  invariant(isStrong(otherDir), '`otherDir` must be a strong direction to be converted to HTML Direction');
  return dir === otherDir ? null : getHTMLDir(dir);
} // == Global Direction ==

/**
 * Set the global direction.
 */


function setGlobalDir(dir: BidiDirection): void {
  globalDir = dir;
}
/**
 * Initialize the global direction
 */


function initGlobalDir(): void {
  setGlobalDir(LTR);
}
/**
 * Get the global direction
 */


function getGlobalDir(): BidiDirection {
  if (!globalDir) {
    this.initGlobalDir();
  }

  invariant(globalDir, 'Global direction not set.');
  return globalDir;
}

const UnicodeBidiDirection = {
  // Values
  NEUTRAL,
  LTR,
  RTL,
  // Helpers
  isStrong,
  getHTMLDir,
  getHTMLDirIfDifferent,
  // Global Direction
  setGlobalDir,
  initGlobalDir,
  getGlobalDir
};
module.exports = UnicodeBidiDirection;