{"name": "inline-style-prefixer", "version": "7.0.1", "description": "Run-time Autoprefixer for JavaScript style objects", "module": "es/index.js", "jsnext:main": "es/index.js", "main": "lib/index.js", "files": ["LICENSE", "README.md", "lib/", "es/"], "scripts": {"babel:es": "babel -d es modules --ignore __tests__", "babel:lib": "cross-env BABEL_ENV=commonjs babel -d lib modules --ignore __tests__", "babel": "yarn babel:es && yarn babel:lib", "build": "yarn run check && yarn generate && yarn babel", "check": "yarn clear && yarn format && yarn lint && yarn coverage", "clear": "rimraf lib es coverage _book", "docs": "gitbook install && gitbook build && gh-pages -d _book", "flow": "flow", "format": "prettier --write \"./modules/**/*.js\"", "generate": "cross-env BABEL_ENV=commonjs babel-node generateDefaultData", "lint": "eslint modules/**/*.js", "release": "yarn build && npm publish", "test": "cross-env BABEL_ENV=commonjs jest", "coverage": "yarn test --coverage"}, "repository": "https://github.com/robinweser/inline-style-prefixer", "keywords": ["react", "react styling", "prefixer", "inline styles", "autoprefixer", "vendor prefix", "userAgent"], "author": "<PERSON>", "license": "MIT", "jest": {"rootDir": "modules"}, "dependencies": {"css-in-js-utils": "^3.1.0"}, "devDependencies": {"babel": "^6.5.2", "babel-cli": "^6.6.0", "babel-core": "^6.6.0", "babel-eslint": "^7.1.1", "babel-jest": "^20.0.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-es2015": "^6.6.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-0": "^6.5.0", "caniuse-api": "^3.0.0", "cross-env": "^5.2.0", "eslint": "^5.0.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2", "eslint-plugin-react": "^6.9.0", "gh-pages": "^1.2.0", "gitbook": "^3.2.2", "gitbook-cli": "^2.3.0", "gitbook-plugin-anker-enable": "0.0.4", "gitbook-plugin-edit-link": "^2.0.2", "gitbook-plugin-github": "^2.0.0", "gitbook-plugin-prism": "^2.4.0", "jest": "^19.0.2", "prettier": "^2.2.1", "rimraf": "^2.6.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}