export default {
  'border-radius': 'borderRadius',
  'border-image': ['borderImage', 'borderImageOutset', 'borderImageRepeat', 'borderImageSlice', 'borderImageSource', 'borderImageWidth'],
  flexbox: ['flex', 'flexBasis', 'flexDirection', 'flexGrow', 'flexFlow', 'flexShrink', 'flexWrap', 'alignContent', 'alignItems', 'alignSelf', 'justifyContent', 'order'],
  'css-transitions': ['transition', 'transitionDelay', 'transitionDuration', 'transitionProperty', 'transitionTimingFunction'],
  transforms2d: ['transform', 'transformOrigin', 'transformOriginX', 'transformOriginY'],
  transforms3d: ['backfaceVisibility', 'perspective', 'perspectiveOrigin', 'transform', 'transformOrigin', 'transformStyle', 'transformOriginX', 'transformOriginY', 'transformOriginZ'],
  'css-animation': ['animation', 'animationDelay', 'animationDirection', 'animationFillMode', 'animationDuration', 'animationIterationCount', 'animationName', 'animationPlayState', 'animationTimingFunction'],
  'css-appearance': 'appearance',
  'user-select-none': 'userSelect',
  'css-backdrop-filter': 'backdropFilter',
  'css3-boxsizing': 'boxSizing',
  'font-kerning': 'fontKerning',
  'css-exclusions': ['wrapFlow', 'wrapThrough', 'wrapMargin'],
  'css-snappoints': ['scrollSnapType', 'scrollSnapPointsX', 'scrollSnapPointsY', 'scrollSnapDestination', 'scrollSnapCoordinate'],
  'text-emphasis': ['textEmphasisPosition', 'textEmphasis', 'textEmphasisStyle', 'textEmphasisColor'],
  'css-text-align-last': 'textAlignLast',
  'css-boxdecorationbreak': 'boxDecorationBreak',
  'css-clip-path': 'clipPath',
  'css-masks': ['maskImage', 'maskMode', 'maskRepeat', 'maskPosition', 'maskClip', 'maskOrigin', 'maskSize', 'maskComposite', 'mask', 'maskBorderSource', 'maskBorderMode', 'maskBorderSlice', 'maskBorderWidth', 'maskBorderOutset', 'maskBorderRepeat', 'maskBorder', 'maskType'],
  'css-touch-action': 'touchAction',
  'text-size-adjust': 'textSizeAdjust',
  'text-decoration': ['textDecorationStyle', 'textDecorationSkip', 'textDecorationLine', 'textDecorationColor'],
  'css-shapes': ['shapeImageThreshold', 'shapeImageMargin', 'shapeImageOutside'],
  'css3-tabsize': 'tabSize',
  'css-filters': 'filter',
  'css-resize': 'resize',
  'css-hyphens': 'hyphens',
  'css-regions': ['flowInto', 'flowFrom', 'breakBefore', 'breakAfter', 'breakInside', 'regionFragment'],
  'object-fit': ['objectFit', 'objectPosition'],
  'text-overflow': 'textOverflow',
  'background-img-opts': ['backgroundClip', 'backgroundOrigin', 'backgroundSize'],
  'font-feature': 'fontFeatureSettings',
  'css-boxshadow': 'boxShadow',
  multicolumn: ['breakAfter', 'breakBefore', 'breakInside', 'columnCount', 'columnFill', 'columnGap', 'columnRule', 'columnRuleColor', 'columnRuleStyle', 'columnRuleWidth', 'columns', 'columnSpan', 'columnWidth', 'columnGap'],
  'css-writing-mode': ['writingMode'],
  'css-text-orientation': ['textOrientation']
};