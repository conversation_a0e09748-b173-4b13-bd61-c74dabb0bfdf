// values are "up-to"
var maximumVersion = 9999;

export default {
  calc: {
    firefox: 15,
    chrome: 25,
    safari: 6.1,
    ios_saf: 7
  },
  crossFade: {
    chrome: maximumVersion,
    opera: maximumVersion,
    and_chr: maximumVersion,
    ios_saf: 10,
    safari: 10
  },
  cursor: {
    firefox: 24,
    chrome: 37,
    safari: 9,
    opera: 24
  },
  filter: {
    ios_saf: 9.3,
    safari: 9.1
  },
  flex: {
    chrome: 29,
    safari: 9,
    ios_saf: 9,
    opera: 16
  },
  flexboxIE: {
    ie: 11
  },
  flexboxOld: {
    firefox: 22,
    chrome: 21,
    safari: 6.2,
    ios_saf: 6.2,
    android: 4.4
  },
  gradient: {
    firefox: 16,
    chrome: 26,
    safari: 7,
    ios_saf: 7,
    opera: 12.1,
    op_mini: 12.1,
    android: 4.4
  },
  grid: {
    edge: 16,
    ie: 11
  },
  imageSet: {
    chrome: maximumVersion,
    safari: maximumVersion,
    opera: maximumVersion,
    and_chr: maximumVersion,
    ios_saf: maximumVersion
  },
  logical: {
    chrome: 68,
    safari: 12,
    opera: 55,
    and_chr: 66,
    ios_saf: 12,
    firefox: 40
  },
  position: {
    safari: 12.1,
    ios_saf: 12.4
  },
  sizing: {
    chrome: 46,
    safari: 10.1,
    opera: 33,
    and_chr: 53,
    ios_saf: maximumVersion
  },
  transition: {
    chrome: maximumVersion,
    safari: maximumVersion,
    opera: maximumVersion,
    and_chr: maximumVersion,
    and_uc: maximumVersion,
    ios_saf: maximumVersion,
    msie: maximumVersion,
    edge: maximumVersion,
    firefox: maximumVersion,
    op_mini: maximumVersion
  }
};