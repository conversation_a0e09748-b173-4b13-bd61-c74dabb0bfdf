# hyphenate-style-name

[![npm version](http://img.shields.io/npm/v/hyphenate-style-name.svg?style=flat-square)](https://www.npmjs.com/package/hyphenate-style-name)[![npm](https://img.shields.io/npm/dm/hyphenate-style-name.svg?style=flat-square)](https://www.npmjs.com/package/hyphenate-style-name)[![npm bundle size](https://img.shields.io/bundlephobia/minzip/hyphenate-style-name.svg?style=flat-square)](https://bundlephobia.com/result?p=hyphenate-style-name)

Hyphenates a camelcased CSS property name. For example:

- `backgroundColor` => `background-color`
- `MozTransition` => `-moz-transition`
- `msTransition` => `-ms-transition`
- `color` => `color`

# Installation

```bash
$ npm install --save hyphenate-style-name
```

# Usage

```js
import hyphenateStyleName from 'hyphenate-style-name'

console.log(hyphenateStyleName('MozTransition')) // -moz-transition
```

# License

BSD-3-Clause licensed. See LICENSE.
