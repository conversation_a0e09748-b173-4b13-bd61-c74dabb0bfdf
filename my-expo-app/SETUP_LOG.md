# Setup Log - React Native Expo App

Log detail dari proses setup aplikasi React Native dengan Expo yang berhasil dilakukan.

## 🕐 Timeline Setup

### Step 1: Project Creation (00:00 - 00:25)
```bash
npx create-expo-app@latest my-expo-app --template blank
```

**Output**:
```
Creating an Expo project using the blank template.
✔ Downloaded and extracted project files.
> npm install
added 649 packages, and audited 650 packages in 25s
60 packages are looking for funding
found 0 vulnerabilities
✅ Your project is ready!
```

**Hasil**: Project berhasil dibuat dengan 649 dependencies terinstall

### Step 2: First Run Attempt (00:25 - 00:30)
```bash
cd my-expo-app && npm start
```

**Error Encountered**:
```
Error: ENOSPC: System limit for number of file watchers reached
errno: -28, syscall: 'watch', code: 'ENOSPC'
```

**Root Cause**: Linux file watcher limit terlalu rendah untuk Metro bundler

### Step 3: Troubleshooting File Watchers (00:30 - 00:35)
**Attempted Solution**:
```bash
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

**Status**: Memerlukan sudo password, skip untuk solusi alternatif

### Step 4: Alternative - Web Mode (00:35 - 00:40)
```bash
npm run web
```

**Error Encountered**:
```
CommandError: It looks like you're trying to use web support but don't have the required dependencies installed.
Install react-dom@19.0.0, react-native-web@^0.20.0, @expo/metro-runtime@~5.0.4
```

### Step 5: Install Web Dependencies (00:40 - 00:45)
```bash
npx expo install react-dom react-native-web @expo/metro-runtime
```

**Output**:
```
Installing 3 SDK 53.0.0 compatible native modules using npm
added 21 packages, and audited 671 packages in 5s
62 packages are looking for funding
found 0 vulnerabilities
```

### Step 6: Successful Launch (00:45 - 01:30)
```bash
npm run web
```

**Output**:
```
Starting project at /path/to/my-expo-app
Starting Metro Bundler

[QR Code displayed]

› Metro waiting on exp://************:8081
› Web is waiting on http://localhost:8081
› Using Expo Go

Web ./index.js ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓░ 99.9% (166/166)
Web Bundled 3544ms index.js (166 modules)
```

**Status**: ✅ Berhasil! Server berjalan di port 8081

### Step 7: Verification (01:30 - 01:35)
```bash
curl -I http://localhost:8081
```

**Response**:
```
HTTP/1.1 200 OK
Content-Type: text/html
Date: Sat, 19 Jul 2025 15:39:23 GMT
Connection: keep-alive
```

**Status**: ✅ Server responding correctly

## 📊 Final Statistics

- **Total Setup Time**: ~1.5 menit
- **Dependencies Installed**: 671 packages
- **Bundle Modules**: 166 modules
- **Bundle Time**: 3.544 seconds
- **Server Port**: 8081
- **Bundle Size**: Development mode

## 🔍 Key Learnings

### 1. File Watcher Issue
- **Problem**: Metro bundler membutuhkan banyak file watchers
- **Linux Default**: Biasanya 8192 (terlalu kecil)
- **Recommended**: 524288
- **Workaround**: Gunakan web mode yang lebih stabil

### 2. Web Dependencies
- **Not Included by Default**: Template blank tidak include web deps
- **Required for Web**: react-dom, react-native-web, @expo/metro-runtime
- **Auto-Install**: Expo CLI memberikan perintah yang tepat

### 3. Development Server
- **Metro Bundler**: JavaScript bundler untuk React Native
- **Hot Reload**: Otomatis aktif untuk development
- **Multi-Platform**: Satu server untuk web, iOS, Android
- **QR Code**: Untuk testing di device fisik

## 🛠️ Commands Reference

### Development
```bash
npm start          # Start with QR code
npm run web        # Web only
npm run android    # Android only  
npm run ios        # iOS only (macOS required)
```

### Debugging
```bash
npx expo start --dev --minify=false    # Debug mode
npx expo start --no-dev --minify       # Production-like
npx expo start --localhost             # Local only
npx expo start --tunnel                # Public tunnel
```

### Build & Export
```bash
npx expo export                        # Export for production
npx expo build:web                     # Build web version
npx expo build:android                 # Build Android APK
npx expo build:ios                     # Build iOS (macOS only)
```

## 🎯 Success Indicators

✅ **Metro bundler started successfully**
✅ **No compilation errors**  
✅ **Web server responding on port 8081**
✅ **QR code generated for mobile testing**
✅ **Hot reload working**
✅ **Bundle completed in reasonable time**

## 📱 Testing Checklist

- [ ] Web browser: http://localhost:8081
- [ ] Android: Scan QR with Expo Go
- [ ] iOS: Scan QR with Camera app
- [ ] Hot reload: Edit App.js and see changes
- [ ] Error handling: Introduce syntax error and check

---

**Final Status**: 🎉 **SETUP SUCCESSFUL**

Aplikasi React Native dengan Expo berhasil dibuat dan berjalan dengan tampilan default "Open up App.js to start working on your app!"
