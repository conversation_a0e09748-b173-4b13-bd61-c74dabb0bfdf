# React Native Expo App - Dokumentasi Setup

Dokumentasi lengkap langkah-langkah pembuatan aplikasi React Native dengan Expo dari awal hingga berjalan.

## 📋 Langkah-langkah Setup

### 1. Membuat Project Baru
```bash
npx create-expo-app@latest my-expo-app --template blank
```

**Penjelasan:**
- <PERSON><PERSON><PERSON><PERSON> `create-expo-app` untuk membuat project baru
- Template `blank` memberikan struktur dasar yang minimal
- Project dibuat dalam folder `my-expo-app`

### 2. Masuk ke Direktori Project
```bash
cd my-expo-app
```

### 3. Mengatasi Error File Watchers (Jika Terjadi)
Jika muncul error `ENOSPC: System limit for number of file watchers reached`, jalankan:
```bash
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 4. Install Dependencies untuk Web Support
```bash
npx expo install react-dom react-native-web @expo/metro-runtime
```

**Penjelasan:**
- `react-dom`: Di<PERSON><PERSON>an untuk rendering di web browser
- `react-native-web`: Mengkonversi komponen React Native ke web
- `@expo/metro-runtime`: Runtime untuk Metro bundler

### 5. Menjalankan Aplikasi
```bash
npm run web
```

**Alternatif perintah:**
- `npm run android` - Untuk Android emulator/device
- `npm run ios` - Untuk iOS simulator (hanya di macOS)
- `npx expo start` - Mode development dengan QR code

## 🏗️ Struktur Project

```
my-expo-app/
├── App.js              # Komponen utama aplikasi
├── app.json           # Konfigurasi Expo
├── package.json       # Dependencies dan scripts
├── index.js           # Entry point aplikasi
├── assets/            # Gambar, icon, dan asset lainnya
│   ├── icon.png
│   ├── splash-icon.png
│   ├── adaptive-icon.png
│   └── favicon.png
└── node_modules/      # Dependencies yang terinstall
```

## 📱 File Utama

### App.js
```javascript
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View } from 'react-native';

export default function App() {
  return (
    <View style={styles.container}>
      <Text>Open up App.js to start working on your app!</Text>
      <StatusBar style="auto" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
```

### package.json (Scripts)
```json
{
  "scripts": {
    "start": "expo start",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web"
  }
}
```

## 🚀 Cara Menjalankan

### Development Server
```bash
npm run web
```
- Aplikasi akan berjalan di `http://localhost:8081`
- Hot reload aktif (perubahan code langsung terlihat)
- QR code tersedia untuk testing di device

### Testing di Device
1. Install **Expo Go** app di smartphone
2. Scan QR code yang muncul di terminal
3. Aplikasi akan terbuka di Expo Go

## 🛠️ Troubleshooting

### Error: File Watchers Limit
```bash
# Solusi untuk Linux
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### Error: Web Dependencies Missing
```bash
npx expo install react-dom react-native-web @expo/metro-runtime
```

### Port Sudah Digunakan
```bash
# Gunakan port lain
npx expo start --web --port 3000
```

## 📦 Dependencies Utama

- **expo**: Framework utama
- **react**: Library UI
- **react-native**: Platform mobile
- **react-dom**: Rendering untuk web
- **react-native-web**: Komponen web
- **expo-status-bar**: Status bar component

## 🎯 Next Steps

1. Edit `App.js` untuk mengembangkan UI
2. Tambah screen baru di folder `screens/`
3. Install library tambahan sesuai kebutuhan:
   ```bash
   npx expo install expo-router  # Untuk navigation
   npx expo install expo-camera  # Untuk kamera
   npx expo install expo-location  # Untuk GPS
   ```

## 📚 Resources

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [Expo Snack](https://snack.expo.dev/) - Online playground

## 🔧 Detail Teknis

### Proses yang Terjadi Saat Setup

1. **Create Expo App**:
   - Download template blank dari Expo
   - Extract project files
   - Install dependencies dasar (649 packages)
   - Setup konfigurasi Metro bundler

2. **Web Dependencies Installation**:
   - `react-dom@19.0.0`: Untuk DOM rendering
   - `react-native-web@^0.20.0`: Bridge RN ke web
   - `@expo/metro-runtime@~5.0.4`: Runtime Metro

3. **Development Server**:
   - Metro bundler starts pada port 8081
   - Hot reload dan fast refresh aktif
   - QR code generation untuk mobile testing

### File Konfigurasi Penting

#### app.json
```json
{
  "expo": {
    "name": "my-expo-app",
    "slug": "my-expo-app",
    "version": "1.0.0",
    "platforms": ["ios", "android", "web"],
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "light",
    "splash": {
      "image": "./assets/splash-icon.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "assetBundlePatterns": ["**/*"],
    "ios": {
      "supportsTablet": true
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      }
    },
    "web": {
      "favicon": "./assets/favicon.png"
    }
  }
}
```

#### index.js (Entry Point)
```javascript
import { registerRootComponent } from 'expo';
import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
```

## 🐛 Error yang Mungkin Terjadi

### 1. ENOSPC Error (File Watchers)
**Error**: `System limit for number of file watchers reached`

**Penyebab**: Linux memiliki limit default untuk file watchers yang terlalu kecil

**Solusi**:
```bash
# Temporary fix
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Atau restart sistem setelah edit /etc/sysctl.conf
```

### 2. Web Dependencies Missing
**Error**: `It looks like you're trying to use web support but don't have the required dependencies`

**Solusi**:
```bash
npx expo install react-dom react-native-web @expo/metro-runtime
```

### 3. Port Already in Use
**Error**: `Port 8081 is already in use`

**Solusi**:
```bash
# Kill process menggunakan port 8081
lsof -ti:8081 | xargs kill -9

# Atau gunakan port lain
npx expo start --web --port 3000
```

## 📊 Performance Tips

1. **Development Mode**:
   - Gunakan `--dev` flag untuk debugging
   - `--minify` untuk production testing

2. **Bundle Size**:
   - Check bundle dengan `npx expo export`
   - Gunakan `expo-bundle-analyzer` untuk analisis

3. **Hot Reload**:
   - Fast refresh otomatis aktif
   - Ctrl+R untuk manual reload

## 🔄 Workflow Development

1. **Edit Code**: Ubah `App.js` atau file lain
2. **Auto Reload**: Perubahan langsung terlihat
3. **Debug**: Gunakan React DevTools di browser
4. **Test**: Scan QR code untuk test di device
5. **Build**: `expo build` untuk production

---

**Status**: ✅ Aplikasi berhasil dibuat dan berjalan di `http://localhost:8081`

**Waktu Setup**: ~2-3 menit (tergantung koneksi internet)
**Bundle Size**: ~166 modules (development mode)
